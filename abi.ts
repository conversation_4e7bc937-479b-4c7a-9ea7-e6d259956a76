export const abi = [{ "inputs": [{ "internalType": "address", "name": "owner_", "type": "address" }, { "internalType": "contract IVestPresaleScheduler", "name": "scheduler", "type": "address" }, { "internalType": "contract IVestFeeCollectorProvider", "name": "feeCollectorProvider", "type": "address" }, { "components": [{ "components": [{ "internalType": "uint16", "name": "numerator", "type": "uint16" }, { "internalType": "uint16", "name": "denominator", "type": "uint16" }], "internalType": "struct Membership.Fees", "name": "fees", "type": "tuple" }, { "components": [{ "internalType": "address", "name": "token", "type": "address" }, { "internalType": "string", "name": "color", "type": "string" }, { "internalType": "string", "name": "description", "type": "string" }], "internalType": "struct IVestMembership.Metadata", "name": "metadata", "type": "tuple" }, { "internalType": "contract IVestMembershipDescriptor", "name": "descriptor", "type": "address" }], "internalType": "struct Membership.Configuration", "name": "configuration", "type": "tuple" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [], "name": "ERC721EnumerableForbiddenBatchMint", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "sender", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }, { "internalType": "address", "name": "owner", "type": "address" }], "name": "ERC721IncorrectOwner", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "operator", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "ERC721InsufficientApproval", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "approver", "type": "address" }], "name": "ERC721InvalidApprover", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "operator", "type": "address" }], "name": "ERC721InvalidOperator", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }], "name": "ERC721InvalidOwner", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "receiver", "type": "address" }], "name": "ERC721InvalidReceiver", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "sender", "type": "address" }], "name": "ERC721InvalidSender", "type": "error" }, { "inputs": [{ "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "ERC721NonexistentToken", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "ERC721OutOfBoundsIndex", "type": "error" }, { "inputs": [{ "internalType": "uint256", "name": "mintId", "type": "uint256" }], "name": "InvalidMintId", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }], "name": "OwnableInvalidOwner", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "account", "type": "address" }], "name": "OwnableUnauthorizedAccount", "type": "error" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "TransferNotAllowed", "type": "error" }, { "inputs": [], "name": "UnacceptableReference", "type": "error" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "approved", "type": "address" }, { "indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "Approval", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "operator", "type": "address" }, { "indexed": false, "internalType": "bool", "name": "approved", "type": "bool" }], "name": "ApprovalForAll", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "mintId", "type": "uint256" }, { "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": false, "internalType": "bytes", "name": "data", "type": "bytes" }], "name": "DynamicIdNFTMinted", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "uint256", "name": "mintId", "type": "uint256" }, { "indexed": true, "internalType": "uint256", "name": "newPublicId", "type": "uint256" }, { "indexed": false, "internalType": "bytes", "name": "payload", "type": "bytes" }], "name": "DynamicIdNFTUpdated", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "previousOwner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "newOwner", "type": "address" }], "name": "OwnershipTransferred", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "to", "type": "address" }, { "indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "Transfer", "type": "event" }, { "inputs": [{ "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }], "name": "balanceOf", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }, { "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "consume", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }, { "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "extend", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "getApproved", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "getAttributes", "outputs": [{ "components": [{ "internalType": "uint256", "name": "price", "type": "uint256" }, { "internalType": "uint256", "name": "allocation", "type": "uint256" }, { "internalType": "uint256", "name": "claimbackPeriod", "type": "uint256" }, { "internalType": "uint32", "name": "tgeNumerator", "type": "uint32" }, { "internalType": "uint32", "name": "tgeDenominator", "type": "uint32" }, { "internalType": "uint32", "name": "cliffDuration", "type": "uint32" }, { "internalType": "uint32", "name": "cliffNumerator", "type": "uint32" }, { "internalType": "uint32", "name": "cliffDenominator", "type": "uint32" }, { "internalType": "uint32", "name": "vestingPeriodCount", "type": "uint32" }, { "internalType": "uint32", "name": "vestingPeriodDuration", "type": "uint32" }, { "internalType": "uint8", "name": "tradeable", "type": "uint8" }], "internalType": "struct IVestMembership.Attributes", "name": "", "type": "tuple" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "getRoundId", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "getStartTimestamp", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "getUsage", "outputs": [{ "components": [{ "internalType": "uint256", "name": "max", "type": "uint256" }, { "internalType": "uint256", "name": "current", "type": "uint256" }], "internalType": "struct IVestMembership.Usage", "name": "", "type": "tuple" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "address", "name": "operator", "type": "address" }], "name": "isApprovedForAll", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner_", "type": "address" }, { "internalType": "uint256", "name": "roundId", "type": "uint256" }, { "internalType": "uint256", "name": "currentUsage", "type": "uint256" }, { "internalType": "uint256", "name": "maxUsage", "type": "uint256" }, { "components": [{ "internalType": "uint256", "name": "price", "type": "uint256" }, { "internalType": "uint256", "name": "allocation", "type": "uint256" }, { "internalType": "uint256", "name": "claimbackPeriod", "type": "uint256" }, { "internalType": "uint32", "name": "tgeNumerator", "type": "uint32" }, { "internalType": "uint32", "name": "tgeDenominator", "type": "uint32" }, { "internalType": "uint32", "name": "cliffDuration", "type": "uint32" }, { "internalType": "uint32", "name": "cliffNumerator", "type": "uint32" }, { "internalType": "uint32", "name": "cliffDenominator", "type": "uint32" }, { "internalType": "uint32", "name": "vestingPeriodCount", "type": "uint32" }, { "internalType": "uint32", "name": "vestingPeriodDuration", "type": "uint32" }, { "internalType": "uint8", "name": "tradeable", "type": "uint8" }], "internalType": "struct IVestMembership.Attributes", "name": "attributes", "type": "tuple" }], "name": "mint", "outputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "name", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "owner", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "ownerOf", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }, { "internalType": "uint256", "name": "amount", "type": "uint256" }], "name": "reduce", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }, { "internalType": "uint256", "name": "salePrice", "type": "uint256" }], "name": "royaltyInfo", "outputs": [{ "internalType": "address", "name": "", "type": "address" }, { "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "from", "type": "address" }, { "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "from", "type": "address" }, { "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "publicId", "type": "uint256" }, { "internalType": "bytes", "name": "data", "type": "bytes" }], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "operator", "type": "address" }, { "internalType": "bool", "name": "approved", "type": "bool" }], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes4", "name": "interfaceId", "type": "bytes4" }], "name": "supportsInterface", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "symbol", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "tokenByIndex", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "tokenOfOwnerByIndex", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "tokenURI", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "totalSupply", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "from", "type": "address" }, { "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "newOwner", "type": "address" }], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "start", "type": "uint256" }, { "internalType": "uint256", "name": "allocation", "type": "uint256" }, { "components": [{ "internalType": "uint256", "name": "price", "type": "uint256" }, { "internalType": "uint256", "name": "allocation", "type": "uint256" }, { "internalType": "uint256", "name": "claimbackPeriod", "type": "uint256" }, { "internalType": "uint32", "name": "tgeNumerator", "type": "uint32" }, { "internalType": "uint32", "name": "tgeDenominator", "type": "uint32" }, { "internalType": "uint32", "name": "cliffDuration", "type": "uint32" }, { "internalType": "uint32", "name": "cliffNumerator", "type": "uint32" }, { "internalType": "uint32", "name": "cliffDenominator", "type": "uint32" }, { "internalType": "uint32", "name": "vestingPeriodCount", "type": "uint32" }, { "internalType": "uint32", "name": "vestingPeriodDuration", "type": "uint32" }, { "internalType": "uint8", "name": "tradeable", "type": "uint8" }], "internalType": "struct IVestMembership.Attributes", "name": "attributes", "type": "tuple" }], "name": "unlocked", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "publicId", "type": "uint256" }], "name": "unlocked", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }] as const